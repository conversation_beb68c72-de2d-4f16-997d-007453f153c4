using Application.DTOs.Payment;
using Application.DTOs.Sms;
using Application.Interfaces;
using Application.Interfaces.Repositories;
using Application.Interfaces.Services;
using Application.Wrappers;
using MediatR;
using Microsoft.Extensions.Configuration;
using System;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json;
using System.Net.Http;
using System.Text;
using Domain.Settings;
using Microsoft.Extensions.Options;

namespace Application.Features.Payments.PhonePe
{
    public class VerifyPhonePePaymentCommand : IRequest<Response<PhonePeVerifyData>>
    {
        public PhonePeVerifyRequest VerifyRequest { get; set; }
    }

    public class VerifyPhonePePaymentCommandHandler : IRequestHandler<VerifyPhonePePaymentCommand, Response<PhonePeVerifyData>>
    {
        private readonly IBookingRepositoryAsync _bookingRepository;
        private readonly IConfiguration _configuration;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IPhonePeAuthService _phonePeAuthService;
        private readonly ISmsService _smsService;
        private readonly ICloudSmsSettings _iCloudSmsSettings;

        public VerifyPhonePePaymentCommandHandler(
            IBookingRepositoryAsync bookingRepository,
            IConfiguration configuration,
            IHttpClientFactory httpClientFactory,
            IPhonePeAuthService phonePeAuthService,
            ISmsService smsService,
            IOptions<ICloudSmsSettings> iCloudSmsSettings)
        {
            _bookingRepository = bookingRepository;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _phonePeAuthService = phonePeAuthService;
            _smsService = smsService;
            _iCloudSmsSettings = iCloudSmsSettings.Value;
        }

        public async Task<Response<PhonePeVerifyData>> Handle(VerifyPhonePePaymentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                Console.WriteLine($"[VerifyPhonePePayment] Starting payment verification for MerchantTransactionId: {request.VerifyRequest.MerchantTransactionId}, OrderId: {request.VerifyRequest.OrderId}");

                // Get booking details
                var booking = await _bookingRepository.GetByUniqueIdAsync(request.VerifyRequest.MerchantTransactionId);
                Console.WriteLine($"[VerifyPhonePePayment] Booking lookup result - Found: {(booking != null)}, BookingId: {(booking?.BookingID ?? "N/A")}");

                if (booking == null)
                {
                    Console.WriteLine($"[VerifyPhonePePayment] Error: Booking not found for MerchantTransactionId: {request.VerifyRequest.MerchantTransactionId}");
                    return new Response<PhonePeVerifyData>
                    {
                        Succeeded = false,
                        Message = "Booking not found"
                    };
                }

                // Get access token
                Console.WriteLine("[VerifyPhonePePayment] Requesting PhonePe access token");
                var accessToken = await _phonePeAuthService.GetAccessTokenAsync();
                Console.WriteLine($"[VerifyPhonePePayment] Access token received: {(string.IsNullOrEmpty(accessToken) ? "Failed" : "Success")}");

                // Get PhonePe settings
                var phonePeSettings = _configuration.GetSection("PaymentSettings:PhonePe");
                var apiUrl = phonePeSettings["ApiUrl"];
                var merchantId = phonePeSettings["MerchantId"];
                Console.WriteLine($"[VerifyPhonePePayment] Using PhonePe API URL: {apiUrl}, MerchantId: {merchantId}");

                // Call order status API
                var client = _httpClientFactory.CreateClient("PhonePe");
                client.DefaultRequestHeaders.Add("Authorization", $"O-Bearer {accessToken}");

                var statusUrl = $"{apiUrl}/checkout/v2/order/{request.VerifyRequest.MerchantTransactionId}/status";
                Console.WriteLine($"[VerifyPhonePePayment] Calling PhonePe status API: {statusUrl}");

                var response = await client.GetAsync(statusUrl);
                Console.WriteLine($"[VerifyPhonePePayment] PhonePe API Response Status: {response.StatusCode}");

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[VerifyPhonePePayment] Error response from PhonePe API: {errorContent}");
                    return new Response<PhonePeVerifyData>
                    {
                        Succeeded = false,
                        Message = "Failed to verify payment status"
                    };
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[VerifyPhonePePayment] PhonePe API Response Content: {responseContent}");

                var phonePeResponse = JsonSerializer.Deserialize<PhonePeOrderStatusResponse>(responseContent);

                // Update booking payment status
                var phonePeStatus = phonePeResponse.state;
                var transactionId = "";
                var paymentId = "";

                Console.WriteLine($"[VerifyPhonePePayment] Payment Status from PhonePe: {phonePeStatus}");

                // Map PhonePe status to application payment status
                string paymentStatus;
                switch (phonePeStatus?.ToUpper())
                {
                    case PhonePePaymentStatus.SUCCESS:
                    case PhonePePaymentStatus.COMPLETED:  // PhonePe returns COMPLETED for successful payments
                        paymentStatus = "Paid";  // Changed to match PaymentStatus enum
                        break;
                    case PhonePePaymentStatus.FAILED:
                        paymentStatus = "Failed";
                        break;
                    case PhonePePaymentStatus.PENDING:
                        paymentStatus = "Pending";
                        break;
                    default:
                        paymentStatus = "Pending";
                        break;
                }

                Console.WriteLine($"[VerifyPhonePePayment] Mapped Payment Status: {paymentStatus}");

                // Extract transaction details if payment is completed
                string phonePePaymentMethod = null;
                if (paymentStatus == "Paid" && phonePeResponse.paymentDetails != null && phonePeResponse.paymentDetails.Count > 0)
                {
                    var latestPayment = phonePeResponse.paymentDetails[0];
                    transactionId = latestPayment.transactionId;
                    paymentId = latestPayment.transactionId;

                    // Get the detailed payment method from splitInstruments if available
                    if (latestPayment.splitInstruments != null && latestPayment.splitInstruments.Count > 0)
                    {
                        var instrument = latestPayment.splitInstruments[0].instrument;
                        phonePePaymentMethod = instrument?.type; // This gives us "CREDIT_CARD", "DEBIT_CARD", "UPI", etc.
                    }

                    // Fallback to paymentMode if instrument type is not available
                    if (string.IsNullOrEmpty(phonePePaymentMethod))
                    {
                        phonePePaymentMethod = latestPayment.paymentMode; // This gives us "CARD", "UPI", etc.
                    }

                    Console.WriteLine($"[VerifyPhonePePayment] Payment completed - TransactionId: {transactionId}, PaymentId: {paymentId}, PaymentMode: {latestPayment.paymentMode}, InstrumentType: {phonePePaymentMethod}");
                }

                // Update booking with payment details
                Console.WriteLine($"[VerifyPhonePePayment] Updating booking with payment details - Status: {paymentStatus}, PaymentId: {paymentId}, OrderId: {request.VerifyRequest.OrderId}, PhonePePaymentMethod: {phonePePaymentMethod}");
                booking.RazorpayStatus = paymentStatus;
                booking.RazorpayPaymentId = paymentId;
                booking.RazorpayOrderid = request.VerifyRequest.OrderId;

                // Store the PhonePe payment method string - the stored procedure will handle the lookup
                booking.PhonePePaymentMethod = phonePePaymentMethod;
                await _bookingRepository.UpdateAsync(booking);
                Console.WriteLine("[VerifyPhonePePayment] Booking updated successfully");

                // Send booking confirmation SMS if payment is successful
                if (paymentStatus == "Paid" && !string.IsNullOrEmpty(booking.PhoneNumber))
                {
                    await SendBookingConfirmationSms(booking);

                    // Send admin alert SMS
                    await SendAdminBookingAlertSms(booking);
                }

                var verifyResponse = new PhonePeVerifyData
                {
                    PaymentStatus = paymentStatus,
                    TransactionId = transactionId,
                    OrderId = request.VerifyRequest.OrderId,
                    BookingId = booking.BookingID,
                    Amount = booking.Fare ?? 0,
                    PaymentId = paymentId,
                    PaymentType = booking.PaymentType ?? "FULL",
                    PartialPaymentAmount = booking.PartialPaymentAmount,
                    RemainingAmountForDriver = booking.RemainingAmountForDriver
                };

                Console.WriteLine($"[VerifyPhonePePayment] Verification completed successfully for BookingId: {booking.BookingID}");
                return new Response<PhonePeVerifyData>(verifyResponse, "Payment verification completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[VerifyPhonePePayment] Exception occurred: {ex.Message}");
                Console.WriteLine($"[VerifyPhonePePayment] Stack Trace: {ex.StackTrace}");
                return new Response<PhonePeVerifyData>
                {
                    Succeeded = false,
                    Message = $"Error verifying payment: {ex.Message}"
                };
            }
        }

        private async Task SendBookingConfirmationSms(Domain.Entities.RLT_BOOKING booking)
        {
            try
            {
                Console.WriteLine($"[VerifyPhonePePayment] Sending booking confirmation SMS to {booking.PhoneNumber}");

                // Format pickup date and time
                DateTime pickupDateTime;
                if (!DateTime.TryParse(booking.PickUpTime, out pickupDateTime))
                {
                    pickupDateTime = DateTime.Now;
                }
                var formattedPickupDate = pickupDateTime.ToString("dd-MM-yyyy", CultureInfo.InvariantCulture);
                var formattedPickupTime = pickupDateTime.ToString("hh:mm tt", CultureInfo.InvariantCulture);

                // Format booking date and time (current date/time when booking was created)
                var bookingDateTime = DateTime.Now;
                var formattedBookingDate = bookingDateTime.ToString("dd-MM-yyyy", CultureInfo.InvariantCulture);
                var formattedBookingTime = bookingDateTime.ToString("hh:mm tt", CultureInfo.InvariantCulture);

                // Format payment details
                var paymentDetails = FormatPaymentDetails(booking);

                var bookingData = new BookingConfirmationSmsData
                {
                    FromLocation = booking.PickUpCity ?? "N/A",
                    ToLocation = booking.DropOffCity ?? "N/A",
                    BookingDate = formattedBookingDate,
                    BookingTime = formattedBookingTime,
                    PickupDate = formattedPickupDate,
                    PickupTime = formattedPickupTime,
                    CabType = booking.CarCategory ?? "Cab",
                    PaymentDetails = paymentDetails,
                    PhoneNumber = booking.PhoneNumber
                };

                var smsSent = await _smsService.SendBookingConfirmationAsync(bookingData);

                if (smsSent)
                {
                    Console.WriteLine($"[VerifyPhonePePayment] Booking confirmation SMS sent successfully to {booking.PhoneNumber}");
                }
                else
                {
                    Console.WriteLine($"[VerifyPhonePePayment] Failed to send booking confirmation SMS to {booking.PhoneNumber}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[VerifyPhonePePayment] Error sending booking confirmation SMS: {ex.Message}");
            }
        }

        private string FormatPaymentDetails(Domain.Entities.RLT_BOOKING booking)
        {
            var totalFare = booking.Fare ?? 0;
            var paymentType = booking.PaymentType ?? "FULL";

            if (paymentType == "PARTIAL")
            {
                var paidAmount = booking.PartialPaymentAmount ?? 0;
                var remainingAmount = booking.RemainingAmountForDriver ?? 0;
                return $"INR{paidAmount:N0} paid online. Remaining INR{remainingAmount:N0} to be paid to driver.";
            }
            else
            {
                // For full payment, show total amount paid and remaining as 0
                return $"INR{totalFare:N0} paid online. Remaining INR0 to be paid to driver.";
            }
        }

        private async Task SendAdminBookingAlertSms(Domain.Entities.RLT_BOOKING booking)
        {
            try
            {
                Console.WriteLine($"[VerifyPhonePePayment] Sending admin booking alert SMS to {_iCloudSmsSettings.AdminAlertNumber}");

                var totalFare = booking.Fare ?? 0;
                var paymentType = booking.PaymentType ?? "FULL";
                var paidAmount = paymentType == "PARTIAL" ? (booking.PartialPaymentAmount ?? 0) : totalFare;

                var adminAlertData = new AdminBookingAlertSmsData
                {
                    CustomerName = booking.TravelerName ?? "N/A",
                    FromLocation = booking.PickUpCity ?? "N/A",
                    ToLocation = booking.DropOffCity ?? "N/A",
                    TotalFare = totalFare.ToString("N0"),
                    PaidAmount = paidAmount.ToString("N0"),
                    BookingId = booking.BookingID ?? "N/A",
                    AdminPhoneNumber = _iCloudSmsSettings.AdminAlertNumber
                };

                var smsSent = await _smsService.SendAdminBookingAlertAsync(adminAlertData);

                if (smsSent)
                {
                    Console.WriteLine($"[VerifyPhonePePayment] Admin booking alert SMS sent successfully to {_iCloudSmsSettings.AdminAlertNumber}");
                }
                else
                {
                    Console.WriteLine($"[VerifyPhonePePayment] Failed to send admin booking alert SMS to {_iCloudSmsSettings.AdminAlertNumber}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[VerifyPhonePePayment] Error sending admin booking alert SMS: {ex.Message}");
            }
        }
    }
}